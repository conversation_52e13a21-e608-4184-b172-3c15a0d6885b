@echo off
echo BWH Price Tool - CSV Data Generator
echo ====================================
echo.

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo Error: Python is not installed or not in PATH
    echo Please install Python and try again.
    pause
    exit /b 1
)

echo Generating CSV files from database...
echo.

REM Run the CSV generation script
python generate_csv_data.py

if errorlevel 1 (
    echo.
    echo Error: CSV generation failed!
    echo Check the error messages above.
    pause
    exit /b 1
) else (
    echo.
    echo CSV generation completed successfully!
    echo.
    echo Files generated in the 'data' directory:
    echo - customermaster.csv
    echo - salesdata.csv  
    echo - items.csv
    echo - metadata.txt
    echo.
    echo You can now run the BWH Price Tool application.
)

pause
