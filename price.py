import sys
import pandas as pd
from PyQt5.QtWidgets import (QApplication, QWidget, QVBoxLayout, QHBoxLayout, QLabel,
                             QComboBox, QPushButton, QTableWidget, QTableWidgetItem,
                             QLineEdit, QMessageBox)
from PyQt5.QtCore import QThread, pyqtSignal, QTimer, Qt
from PyQt5.QtGui import QColor
from csv_data_manager import CSVDataManager

def get_dark_stylesheet():
    """Return dark mode stylesheet for the application"""
    return """
    QWidget {
        background-color: #2b2b2b;
        color: #ffffff;
        font-family: 'Segoe UI', Arial, sans-serif;
        font-size: 9pt;
    }

    QMainWindow {
        background-color: #2b2b2b;
    }

    QLabel {
        color: #ffffff;
        background-color: transparent;
        padding: 2px;
    }

    QLineEdit {
        background-color: #3c3c3c;
        border: 2px solid #555555;
        border-radius: 4px;
        padding: 5px;
        color: #ffffff;
        selection-background-color: #0078d4;
    }

    QLineEdit:focus {
        border-color: #0078d4;
    }

    QLineEdit:read-only {
        background-color: #404040;
        color: #cccccc;
    }

    QComboBox {
        background-color: #3c3c3c;
        border: 2px solid #555555;
        border-radius: 4px;
        padding: 5px;
        color: #ffffff;
        min-width: 200px;
    }

    QComboBox:hover {
        border-color: #0078d4;
    }

    QComboBox::drop-down {
        subcontrol-origin: padding;
        subcontrol-position: top right;
        width: 20px;
        border-left: 1px solid #555555;
        background-color: #3c3c3c;
    }

    QComboBox::down-arrow {
        image: none;
        border-left: 5px solid transparent;
        border-right: 5px solid transparent;
        border-top: 5px solid #ffffff;
        margin: 5px;
    }

    QComboBox QAbstractItemView {
        background-color: #3c3c3c;
        border: 1px solid #555555;
        selection-background-color: #0078d4;
        color: #ffffff;
        min-width: 400px;
        font-family: 'Consolas', 'Courier New', monospace;
    }

    QPushButton {
        background-color: #0078d4;
        border: none;
        border-radius: 4px;
        padding: 8px 16px;
        color: #ffffff;
        font-weight: bold;
    }

    QPushButton:hover {
        background-color: #106ebe;
    }

    QPushButton:pressed {
        background-color: #005a9e;
    }

    QPushButton:disabled {
        background-color: #555555;
        color: #888888;
    }

    QTableWidget {
        background-color: #3c3c3c;
        alternate-background-color: #404040;
        gridline-color: #555555;
        border: 1px solid #555555;
        selection-background-color: #0078d4;
        color: #ffffff;
    }

    QTableWidget::item {
        padding: 5px;
        border: none;
    }

    QTableWidget::item:selected {
        background-color: #0078d4;
        color: #ffffff;
    }

    QHeaderView::section {
        background-color: #404040;
        color: #ffffff;
        padding: 5px;
        border: 1px solid #555555;
        font-weight: bold;
    }

    QHeaderView::section:hover {
        background-color: #4a4a4a;
    }

    QScrollBar:vertical {
        background-color: #3c3c3c;
        width: 12px;
        border: none;
    }

    QScrollBar::handle:vertical {
        background-color: #555555;
        border-radius: 6px;
        min-height: 20px;
    }

    QScrollBar::handle:vertical:hover {
        background-color: #666666;
    }

    QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
        height: 0px;
    }

    QScrollBar:horizontal {
        background-color: #3c3c3c;
        height: 12px;
        border: none;
    }

    QScrollBar::handle:horizontal {
        background-color: #555555;
        border-radius: 6px;
        min-width: 20px;
    }

    QScrollBar::handle:horizontal:hover {
        background-color: #666666;
    }

    QScrollBar::add-line:horizontal, QScrollBar::sub-line:horizontal {
        width: 0px;
    }

    QMessageBox {
        background-color: #2b2b2b;
        color: #ffffff;
    }

    QMessageBox QPushButton {
        min-width: 80px;
        padding: 6px 12px;
    }
    """

class CSVDataWorker(QThread):
    """Worker thread for CSV data operations to prevent UI freezing"""
    data_ready = pyqtSignal(pd.DataFrame)
    error_occurred = pyqtSignal(str)

    def __init__(self, operation_type, params=None, csv_manager=None):
        super().__init__()
        self.operation_type = operation_type
        self.params = params
        self.csv_manager = csv_manager
        self._is_running = False

    def run(self):
        self._is_running = True
        try:
            if not self._is_running:  # Check if we should stop
                return

            # Use provided csv_manager or create new one
            if self.csv_manager is None:
                self.csv_manager = CSVDataManager()

            # Route to appropriate CSV operation based on operation type
            if self.operation_type == "customers":
                df = self.csv_manager.get_customers()
            elif self.operation_type == "items":
                df = self.csv_manager.get_items()
            elif self.operation_type == "recent_items" and self.params:
                df = self.csv_manager.get_recent_items_for_customer(self.params[0])
            elif self.operation_type == "sales_data" and self.params and len(self.params) >= 2:
                df = self.csv_manager.get_sales_data(self.params[0], self.params[1])
            else:
                df = pd.DataFrame()

            if self._is_running:  # Only emit if still running
                self.data_ready.emit(df)
        except Exception as e:
            if self._is_running:  # Only emit if still running
                self.error_occurred.emit(str(e))
        finally:
            self._is_running = False

    def stop(self):
        """Stop the worker thread"""
        self._is_running = False

    def stop(self):
        """Stop the worker thread gracefully"""
        self._is_running = False
        self.quit()
        self.wait(3000)  # Wait up to 3 seconds for thread to finish

class SalesDataApp(QWidget):
    def __init__(self):
        super().__init__()
        self.csv_manager = CSVDataManager()
        self.worker = None
        self.all_customers = []  # Store all customer codes for filtering
        self.customer_data = {}  # Store customer code -> description mapping
        self.customer_display_items = []  # Store formatted display items (code - description)

        # Timer for debounced search
        self.search_timer = QTimer()
        self.search_timer.setSingleShot(True)
        self.search_timer.timeout.connect(self.perform_customer_filter)

        # Timer for debounced recent items loading
        self.recent_items_timer = QTimer()
        self.recent_items_timer.setSingleShot(True)
        self.recent_items_timer.timeout.connect(self.load_recent_items)

        # Worker threads
        self.worker = None
        self.recent_items_worker = None
        self.initUI()
        self.populate_dropdowns()
        # Initialize recent items table as empty
        self.clear_recent_items()
        # Initialize load items button as disabled
        self.load_items_button.setEnabled(False)
        # Set initial focus to search field
        QTimer.singleShot(100, lambda: self.customer_search.setFocus())

    def initUI(self):
        layout = QVBoxLayout()
        layout.setSpacing(10)
        layout.setContentsMargins(15, 15, 15, 15)



        # Customer selection with search
        customer_label = QLabel("Customer Selection:")
        customer_label.setStyleSheet("font-weight: bold; font-size: 10pt;")

        # Customer search and dropdown layout
        customer_layout = QHBoxLayout()

        # Customer search field
        self.customer_search = QLineEdit()
        self.customer_search.setPlaceholderText("Type customer code or description (6+ chars to show dropdown)...")
        self.customer_search.setMinimumHeight(30)
        self.customer_search.setMaximumWidth(200)  # 12 characters wide approximately
        self.customer_search.textChanged.connect(self.on_search_text_changed)
        self.customer_search.returnPressed.connect(self.on_search_enter_pressed)
        # Override key press events to handle arrow keys
        self.customer_search.keyPressEvent = self.search_key_press_event
        # Ensure focus stays in search field
        self.customer_search.focusInEvent = self.search_focus_in_event

        # Customer dropdown - completely non-focusable, selection only
        self.customer_combo = QComboBox()
        self.customer_combo.setMinimumHeight(30)
        self.customer_combo.setMinimumWidth(350)  # 15 characters wider approximately
        self.customer_combo.setEditable(False)
        self.customer_combo.setMaxVisibleItems(15)  # Show more items in dropdown
        self.customer_combo.setInsertPolicy(QComboBox.NoInsert)
        self.customer_combo.setFocusPolicy(Qt.NoFocus)  # Prevent combo from taking focus
        self.customer_combo.currentTextChanged.connect(self.update_customer_description)
        self.customer_combo.activated.connect(self.on_customer_selected)  # When user clicks/selects

        # Make dropdown completely non-interactive for keyboard
        self.customer_combo.view().setFocusPolicy(Qt.NoFocus)
        self.customer_combo.setStyleSheet(self.customer_combo.styleSheet() + """
            QComboBox:focus { border-color: #555555; }
            QComboBox::drop-down:focus { background-color: #3c3c3c; }
        """)

        # Customer description display
        self.customer_desc_display = QLineEdit()
        self.customer_desc_display.setReadOnly(True)
        self.customer_desc_display.setMinimumHeight(30)
        self.customer_desc_display.setPlaceholderText("Customer description will appear here")
        self.customer_desc_display.setStyleSheet("background-color: #404040; color: #cccccc; text-align: left;")

        search_label = QLabel("Search:")
        search_label.setStyleSheet("font-size: 9pt; color: #cccccc;")
        select_label = QLabel("Code:")
        select_label.setStyleSheet("font-size: 9pt; color: #cccccc;")
        desc_label = QLabel("Selected Customer:")
        desc_label.setStyleSheet("font-size: 9pt; color: #cccccc;")

        # Load Item Sales button
        self.load_items_button = QPushButton("📋 Load Item Sales")
        self.load_items_button.setMaximumWidth(140)
        self.load_items_button.setMinimumHeight(30)
        self.load_items_button.clicked.connect(self.manual_load_recent_items)
        self.load_items_button.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                border: none;
                border-radius: 4px;
                padding: 6px 10px;
                color: #ffffff;
                font-weight: bold;
                font-size: 9pt;
            }
            QPushButton:hover {
                background-color: #218838;
            }
            QPushButton:pressed {
                background-color: #1e7e34;
            }
            QPushButton:disabled {
                background-color: #6c757d;
                color: #adb5bd;
            }
        """)

        customer_layout.addWidget(search_label)
        customer_layout.addWidget(self.customer_search, 2)  # Give search field more space
        customer_layout.addWidget(select_label)
        customer_layout.addWidget(self.customer_combo, 1)   # Customer code dropdown
        customer_layout.addWidget(desc_label)
        customer_layout.addWidget(self.customer_desc_display, 2)  # Description display
        customer_layout.addWidget(self.load_items_button)  # Load items button

        # Item selection
        item_label = QLabel("Item Code:")
        item_label.setStyleSheet("font-weight: bold; font-size: 10pt;")
        self.item_combo = QComboBox()
        self.item_combo.setMinimumHeight(30)

        # Recent items header layout with item selection
        recent_items_header_layout = QHBoxLayout()

        # Recent items label
        recent_items_label = QLabel("Recent Items Sales for Selected Customer:")
        recent_items_label.setStyleSheet("font-weight: bold; font-size: 10pt; color: #cccccc;")

        # Item selection section
        item_label = QLabel("Item Code:")
        item_label.setStyleSheet("font-weight: bold; font-size: 10pt;")
        self.item_combo = QComboBox()
        self.item_combo.setMinimumHeight(30)
        self.item_combo.setMaximumWidth(200)  # Limit width so it doesn't take too much space

        # Instruction text
        instruction_label = QLabel("(Double click in table or select Item)")
        instruction_label.setStyleSheet("font-size: 8pt; color: #999999; font-style: italic;")

        # Show Item Sales button (initially hidden)
        self.show_items_button = QPushButton("📊 Show Item Sales")
        self.show_items_button.setMaximumWidth(130)
        self.show_items_button.setMinimumHeight(30)
        self.show_items_button.clicked.connect(self.show_recent_items_table)
        self.show_items_button.setStyleSheet("""
            QPushButton {
                background-color: #ffc107;
                border: none;
                border-radius: 4px;
                padding: 6px 10px;
                color: #000000;
                font-weight: bold;
                font-size: 9pt;
            }
            QPushButton:hover {
                background-color: #e0a800;
            }
            QPushButton:pressed {
                background-color: #d39e00;
            }
        """)
        self.show_items_button.hide()  # Initially hidden

        # Reset button
        self.reset_button = QPushButton("🔄 Reset Filters")
        self.reset_button.setMaximumWidth(120)
        self.reset_button.setMinimumHeight(30)
        self.reset_button.clicked.connect(self.reset_filters)
        self.reset_button.setStyleSheet("""
            QPushButton {
                background-color: #dc3545;
                border: none;
                border-radius: 4px;
                padding: 6px 10px;
                color: #ffffff;
                font-weight: bold;
                font-size: 9pt;
            }
            QPushButton:hover {
                background-color: #c82333;
            }
            QPushButton:pressed {
                background-color: #bd2130;
            }
        """)

        # Add to header layout
        recent_items_header_layout.addWidget(recent_items_label)
        recent_items_header_layout.addStretch()  # Push item selection to the right
        recent_items_header_layout.addWidget(item_label)
        recent_items_header_layout.addWidget(self.item_combo)
        recent_items_header_layout.addWidget(instruction_label)
        recent_items_header_layout.addWidget(self.show_items_button)
        recent_items_header_layout.addWidget(self.reset_button)

        # Table for recent items - horizontal layout with groups of 5
        self.recent_items_table = QTableWidget()
        self.recent_items_table.setRowCount(5)  # Fixed 5 rows
        # Remove height restrictions to show full table
        self.recent_items_table.setVerticalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        self.recent_items_table.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        # 9 columns: 3 groups of 3 (Code + Description + Date)
        self.recent_items_table.setColumnCount(9)
        self.recent_items_table.setHorizontalHeaderLabels([
            "Code", "Description", "Date",
            "Code", "Description", "Date",
            "Code", "Description", "Date"
        ])
        self.recent_items_table.setAlternatingRowColors(True)
        self.recent_items_table.setSortingEnabled(False)
        self.recent_items_table.setSelectionBehavior(QTableWidget.SelectItems)
        self.recent_items_table.setSelectionMode(QTableWidget.SingleSelection)
        # Connect double-click to select item
        self.recent_items_table.cellDoubleClicked.connect(self.on_recent_item_selected)





        # Fetch button and GP display layout
        fetch_gp_layout = QHBoxLayout()

        # Fetch button - 5% wider than Load Item Sales button
        self.fetch_button = QPushButton("🔍 Fetch Sales Data")
        self.fetch_button.clicked.connect(self.fetch_data)
        self.fetch_button.setMinimumHeight(40)
        self.fetch_button.setMaximumWidth(147)  # 5% wider than 140 (140 * 1.05 = 147)
        self.fetch_button.setStyleSheet("font-size: 11pt; font-weight: bold;")

        # Current GP display - moved to same row
        self.new_gp_label = QLabel("Current Average GP%:")
        self.new_gp_label.setStyleSheet("font-weight: bold; font-size: 10pt;")
        self.new_gp_display = QLineEdit()
        self.new_gp_display.setReadOnly(True)
        self.new_gp_display.setMinimumHeight(30)
        self.new_gp_display.setMaximumWidth(60)  # 20% of original width (approximately)
        self.new_gp_display.setPlaceholderText("Will show after fetching data")

        # Add to layout
        fetch_gp_layout.addWidget(self.fetch_button)
        fetch_gp_layout.addWidget(self.new_gp_label)
        fetch_gp_layout.addWidget(self.new_gp_display)
        fetch_gp_layout.addStretch()  # Push everything to the left

        # Data table
        self.table = QTableWidget()
        self.table.setAlternatingRowColors(True)
        self.table.setSortingEnabled(True)
        # Allow horizontal scrolling if needed but minimize vertical scrolling
        self.table.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        self.table.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)

        # Add widgets to layout
        layout.addWidget(customer_label)
        layout.addLayout(customer_layout)
        layout.addLayout(recent_items_header_layout)
        layout.addWidget(self.recent_items_table)
        layout.addLayout(fetch_gp_layout)
        layout.addWidget(self.table)

        self.setLayout(layout)

    def populate_dropdowns(self):
        """Populate dropdown menus with customer and item codes"""
        try:
            # Load customers from CSV data
            customers_df = self.csv_manager.get_customers()
            if not customers_df.empty:
                self.all_customers = customers_df['CustomerCode'].tolist()
                # Create mapping of customer code to description
                self.customer_data = dict(zip(customers_df['CustomerCode'], customers_df['CustomerDesc']))

                # Create display items that show both code and description
                self.customer_display_items = []
                for _, row in customers_df.iterrows():
                    code = row['CustomerCode']
                    desc = row['CustomerDesc'] if pd.notna(row['CustomerDesc']) else ""
                    display_text = f"{code} - {desc}" if desc else code
                    self.customer_display_items.append(display_text)

                self.customer_combo.addItems(self.customer_display_items)

            # Load items from CSV data
            items_df = self.csv_manager.get_items()
            if not items_df.empty:
                self.item_combo.addItems(items_df['ItemCode'].tolist())

        except Exception as e:
            QMessageBox.critical(self, "Data Error", f"Failed to load dropdown data: {str(e)}")

    def on_search_text_changed(self):
        """Handle search text changes with debouncing"""
        # Stop the previous timer
        self.search_timer.stop()
        # Start a new timer with 100ms delay for more responsive search
        self.search_timer.start(100)

    def perform_customer_filter(self):
        """Perform the actual customer filtering"""
        search_text = self.customer_search.text().upper().strip()

        # Store current selection to try to preserve it
        current_selection = self.customer_combo.currentText()

        # Clear current items
        self.customer_combo.clear()

        if not search_text:
            # If search is empty, show all customers but don't show dropdown
            self.customer_combo.addItems(self.customer_display_items)
            self.customer_combo.hidePopup()
        elif len(search_text) < 6:
            # For searches less than 6 characters, filter but don't show dropdown
            filtered_items = []
            for display_item in self.customer_display_items:
                if search_text in display_item.upper():
                    filtered_items.append(display_item)

            self.customer_combo.addItems(filtered_items)
            self.customer_combo.hidePopup()  # Keep dropdown hidden
        else:
            # For 6+ characters, filter and show dropdown
            filtered_items = []
            for display_item in self.customer_display_items:
                if search_text in display_item.upper():
                    filtered_items.append(display_item)

            self.customer_combo.addItems(filtered_items)

            # Show dropdown if there are filtered results
            if filtered_items:
                if not self.customer_combo.view().isVisible():
                    self.customer_combo.showPopup()
            else:
                self.customer_combo.hidePopup()

        # Try to restore previous selection if it's still in the filtered list
        if current_selection:
            index = self.customer_combo.findText(current_selection)
            if index >= 0:
                self.customer_combo.setCurrentIndex(index)

    def search_key_press_event(self, event):
        """Handle key press events in the search field"""
        if event.key() == Qt.Key_Down:
            # Arrow down: move to next item in dropdown
            if self.customer_combo.count() > 0:
                current_index = self.customer_combo.currentIndex()
                if current_index < self.customer_combo.count() - 1:
                    self.customer_combo.setCurrentIndex(current_index + 1)
                else:
                    self.customer_combo.setCurrentIndex(0)  # Wrap to first
                if not self.customer_combo.view().isVisible():
                    self.customer_combo.showPopup()
        elif event.key() == Qt.Key_Up:
            # Arrow up: move to previous item in dropdown
            if self.customer_combo.count() > 0:
                current_index = self.customer_combo.currentIndex()
                if current_index > 0:
                    self.customer_combo.setCurrentIndex(current_index - 1)
                else:
                    self.customer_combo.setCurrentIndex(self.customer_combo.count() - 1)  # Wrap to last
                if not self.customer_combo.view().isVisible():
                    self.customer_combo.showPopup()
        elif event.key() == Qt.Key_Escape:
            # Escape: hide dropdown and clear search
            self.customer_combo.hidePopup()
            self.customer_search.clear()
        else:
            # For all other keys, use default behavior
            QLineEdit.keyPressEvent(self.customer_search, event)

    def on_search_enter_pressed(self):
        """Handle Enter key press in search field"""
        # If there are items in the dropdown, select the current one
        if self.customer_combo.count() > 0:
            current_index = self.customer_combo.currentIndex()
            if current_index >= 0:
                # Trigger the selection
                self.on_customer_selected(current_index)
            self.customer_combo.hidePopup()

    def on_customer_selected(self, index=None):
        """Handle when user explicitly selects a customer"""
        # This is called when user clicks on dropdown item
        # Update description
        self.update_customer_description()
        # Return focus to search field after a brief delay
        QTimer.singleShot(10, lambda: self.customer_search.setFocus())

    def search_focus_in_event(self, event):
        """Handle focus in event for search field"""
        QLineEdit.focusInEvent(self.customer_search, event)
        # Only show dropdown if search text is 6+ characters
        search_text = self.customer_search.text().strip()
        if len(search_text) >= 6 and self.customer_combo.count() > 0:
            if not self.customer_combo.view().isVisible():
                self.customer_combo.showPopup()

    def reset_filters(self):
        """Reset all filters and selections to initial state"""
        # Stop any running timers
        self.search_timer.stop()
        self.recent_items_timer.stop()

        # Stop any running worker threads
        if self.worker and self.worker.isRunning():
            self.worker.stop()
            self.worker = None

        if self.recent_items_worker and self.recent_items_worker.isRunning():
            self.recent_items_worker.stop()
            self.recent_items_worker = None

        # Clear search field
        self.customer_search.clear()

        # Reset customer dropdown to show all customers
        self.customer_combo.clear()
        if self.customer_display_items:
            self.customer_combo.addItems(self.customer_display_items)

        # Clear customer description
        self.customer_desc_display.clear()

        # Reset item dropdown selection
        if self.item_combo.count() > 0:
            self.item_combo.setCurrentIndex(0)

        # Clear GP display
        self.new_gp_display.clear()

        # Clear recent items table and show it again
        self.clear_recent_items()
        self.recent_items_table.show()
        # Hide the "Show Item Sales" button
        self.show_items_button.hide()

        # Reset load items button
        self.load_items_button.setEnabled(False)
        self.load_items_button.setText("📋 Load Item Sales")

        # Clear main data table
        self.table.setRowCount(0)
        self.table.setColumnCount(0)

        # Reset fetch button
        self.fetch_button.setEnabled(True)
        self.fetch_button.setText("🔍 Fetch Sales Data")

        # Set focus back to search field
        QTimer.singleShot(100, lambda: self.customer_search.setFocus())

    def show_recent_items_table(self):
        """Show the Recent Items table and hide the Show Item Sales button"""
        self.recent_items_table.show()
        self.show_items_button.hide()

    def manual_load_recent_items(self):
        """Manually load recent items when user clicks the button"""
        customer_code = self.get_selected_customer_code()
        if not customer_code:
            QMessageBox.warning(self, "No Customer Selected", "Please select a customer first.")
            return

        # Disable button during loading
        self.load_items_button.setEnabled(False)
        self.load_items_button.setText("Loading...")

        # Load recent items
        self.load_recent_items()

    def load_recent_items(self):
        """Load recent items for the selected customer"""
        customer_code = self.get_selected_customer_code()
        if not customer_code:
            self.clear_recent_items()
            return

        # Stop any existing recent items worker
        if self.recent_items_worker and self.recent_items_worker.isRunning():
            self.recent_items_worker.stop()
            self.recent_items_worker = None

        # Show loading indicator
        self.show_recent_items_loading()

        try:
            # Use worker thread for CSV data query
            self.recent_items_worker = CSVDataWorker("recent_items", [customer_code], self.csv_manager)
            self.recent_items_worker.data_ready.connect(self.display_recent_items)
            self.recent_items_worker.error_occurred.connect(self.handle_recent_items_error)
            self.recent_items_worker.finished.connect(self.cleanup_recent_items_worker)
            self.recent_items_worker.start()

        except Exception as e:
            print(f"Error loading recent items: {e}")
            self.clear_recent_items()

    def show_recent_items_loading(self):
        """Show loading indicator in recent items table"""
        # Clear all cells first
        self.clear_recent_items()
        # Show loading message in the first cell
        loading_item = QTableWidgetItem("Loading recent items...")
        self.recent_items_table.setItem(0, 0, loading_item)

    def cleanup_recent_items_worker(self):
        """Clean up the recent items worker thread"""
        # Reset the load items button
        self.load_items_button.setEnabled(True)
        self.load_items_button.setText("📋 Load Item Sales")

        if self.recent_items_worker:
            # Disconnect all signals to prevent issues
            try:
                self.recent_items_worker.data_ready.disconnect()
                self.recent_items_worker.error_occurred.disconnect()
                self.recent_items_worker.finished.disconnect()
            except:
                pass  # Ignore if already disconnected

            self.recent_items_worker.deleteLater()
            self.recent_items_worker = None

    def display_recent_items(self, data):
        """Display recent items in the table with horizontal layout"""
        if data.empty:
            self.clear_recent_items()
            return

        # Clear all cells first
        for row in range(5):
            for col in range(9):
                self.recent_items_table.setItem(row, col, QTableWidgetItem(""))

        # Fill table with data in horizontal groups
        for i, row_data in data.iterrows():
            if i >= 15:  # Limit to 15 items (3 groups of 5)
                break

            # Determine which group (0-2) and position within group (0-4)
            group = i // 5  # 0 for items 1-5, 1 for items 6-10, 2 for items 11-15
            position = i % 5  # Position within the group (0-4)

            # Calculate column offsets for each group (3 columns per group: Code, Description, Date)
            col_offset = group * 3

            # Item Code
            item_code = QTableWidgetItem(str(row_data['ItemCode']))
            self.recent_items_table.setItem(position, col_offset, item_code)

            # Description
            description = QTableWidgetItem(str(row_data['Description']))
            self.recent_items_table.setItem(position, col_offset + 1, description)

            # Last Sale Date - format as date only (YYYY-MM-DD)
            last_sale_date = row_data['LastSaleDate']
            if pd.notna(last_sale_date):
                # Convert to datetime if it's not already, then format as date only
                if isinstance(last_sale_date, str):
                    last_sale_date = pd.to_datetime(last_sale_date)
                formatted_date = last_sale_date.strftime('%Y-%m-%d')
            else:
                formatted_date = ""
            last_sale = QTableWidgetItem(formatted_date)
            self.recent_items_table.setItem(position, col_offset + 2, last_sale)

        # Set dynamic column widths with maximum limits
        self.set_dynamic_column_widths()

        # Resize table to fit content
        self.recent_items_table.resizeRowsToContents()

    def set_dynamic_column_widths(self):
        """Set specific column widths and row height"""
        # Set specific column widths
        for col in range(9):
            col_type = col % 3  # 0=code, 1=description, 2=date

            if col_type == 0:  # Code column - 16 characters wide
                self.recent_items_table.setColumnWidth(col, 128)  # Approximately 16 characters
            elif col_type == 1:  # Description column - 22 characters wide
                self.recent_items_table.setColumnWidth(col, 176)  # Approximately 22 characters
            else:  # Date column
                self.recent_items_table.setColumnWidth(col, 100)

        # Set row height 10% smaller
        default_row_height = self.recent_items_table.verticalHeader().defaultSectionSize()
        new_row_height = int(default_row_height * 0.9)  # 10% smaller
        for row in range(5):
            self.recent_items_table.setRowHeight(row, new_row_height)

    def clear_recent_items(self):
        """Clear the recent items table"""
        # Clear all cells but keep the structure
        for row in range(5):
            for col in range(9):
                self.recent_items_table.setItem(row, col, QTableWidgetItem(""))

    def handle_recent_items_error(self, error_message):
        """Handle errors when loading recent items"""
        print(f"Error loading recent items: {error_message}")
        self.clear_recent_items()
        # Reset the load items button
        self.load_items_button.setEnabled(True)
        self.load_items_button.setText("📋 Load Item Sales")
        QMessageBox.warning(self, "Error", f"Failed to load recent items: {error_message}")

    def on_recent_item_selected(self, row, column):
        """Handle when user double-clicks on a recent item"""
        # Determine which group was clicked based on column (3 columns per group: Code, Description, Date)
        col_type = column % 3

        if col_type == 0:  # Item code column
            item_code_item = self.recent_items_table.item(row, column)
        elif col_type == 1:  # Description column - get the item code from previous column
            item_code_item = self.recent_items_table.item(row, column - 1)
        else:  # Date column - get the item code from 2 columns back
            item_code_item = self.recent_items_table.item(row, column - 2)

        if item_code_item and item_code_item.text().strip():
            item_code = item_code_item.text()

            # Find and select this item in the item combo
            index = self.item_combo.findText(item_code)
            if index >= 0:
                self.item_combo.setCurrentIndex(index)
            else:
                # If item not found in combo, add it temporarily
                self.item_combo.addItem(item_code)
                self.item_combo.setCurrentText(item_code)

    def update_customer_description(self):
        """Update the customer description display when selection changes"""
        selected_display_text = self.customer_combo.currentText()
        if selected_display_text:
            # Extract customer code from display text (format: "CODE - DESCRIPTION")
            customer_code = selected_display_text.split(" - ")[0] if " - " in selected_display_text else selected_display_text
            if customer_code in self.customer_data:
                description = self.customer_data[customer_code]
                self.customer_desc_display.setText(description)
                # Enable the load items button
                self.load_items_button.setEnabled(True)
            else:
                self.customer_desc_display.clear()
                self.load_items_button.setEnabled(False)
        else:
            self.customer_desc_display.clear()
            self.load_items_button.setEnabled(False)

    def get_selected_customer_code(self):
        """Get the customer code from the currently selected item"""
        selected_display_text = self.customer_combo.currentText()
        if selected_display_text:
            # Extract customer code from display text (format: "CODE - DESCRIPTION")
            return selected_display_text.split(" - ")[0] if " - " in selected_display_text else selected_display_text
        return ""

    def fetch_data(self):
        """Fetch sales data using worker thread"""
        customer_code = self.get_selected_customer_code()
        item_code = self.item_combo.currentText()

        if not customer_code or not item_code:
            QMessageBox.warning(self, "Selection Required", "Please select both customer and item codes.")
            return

        # Stop any existing worker
        if self.worker and self.worker.isRunning():
            self.worker.stop()
            self.worker = None

        # Hide the Recent Items table but keep the header row visible
        self.recent_items_table.hide()
        # Show the "Show Item Sales" button
        self.show_items_button.show()

        # Disable button during fetch
        self.fetch_button.setEnabled(False)
        self.fetch_button.setText("Loading...")

        # Create and start worker thread for CSV data
        self.worker = CSVDataWorker("sales_data", [customer_code, item_code], self.csv_manager)
        self.worker.data_ready.connect(self.display_sales_data)
        self.worker.error_occurred.connect(self.show_error)
        self.worker.finished.connect(self.reset_fetch_button)
        self.worker.finished.connect(self.cleanup_main_worker)
        self.worker.start()

    def cleanup_main_worker(self):
        """Clean up the main worker thread"""
        if self.worker:
            # Disconnect all signals to prevent issues
            try:
                self.worker.data_ready.disconnect()
                self.worker.error_occurred.disconnect()
                self.worker.finished.disconnect()
            except:
                pass  # Ignore if already disconnected

            self.worker.deleteLater()
            self.worker = None

    def display_sales_data(self, data):
        """Display the fetched sales data in the table"""
        if data.empty:
            QMessageBox.warning(self, "No Data", "No records found for the selected criteria.")
            return

        # Handle NaN values - fill with appropriate defaults
        data = data.fillna({
            'DDate': '',
            'CustomerCode': '',
            'ItemCode': '',
            'SalesmanCode': '',
            'Description': '',
            'CostPrice': 0,
            'Qty': 0,
            'UnitPrice': 0
        })

        # Calculate GP and GP%
        data['GP'] = data['UnitPrice'] - data['CostPrice']
        data['GP%'] = (data['GP'] / data['UnitPrice']) * 100

        # Add new columns
        data['NEW CostPrice'] = data['CostPrice']  # Default to CostPrice
        data['NEW GP'] = data['GP']         # Default to GP
        data['NEW GP%'] = data['GP%']       # Default to GP%
        # Calculate NEW Price correctly: NEW Price = NEW CostPrice / (1 - NEW GP%/100)
        data['NEW Price'] = data['NEW CostPrice'] / (1 - data['NEW GP%'] / 100)
        data['  '] = ''  # Placeholder column (2 characters wide)

        # Reorder columns: financial data first, then customer/item info
        column_order = ['DDate', 'CostPrice', 'Qty', 'UnitPrice', 'GP', 'GP%',
                       'NEW CostPrice', 'NEW GP', 'NEW GP%', 'NEW Price', '  ',
                       'CustomerCode', 'ItemCode', 'SalesmanCode', 'Description']

        # Reorder the dataframe columns
        data = data.reindex(columns=column_order)

        # Update GP% display
        self.new_gp_display.setText(f"{data['GP%'].mean():.2f}%")

        # Temporarily disconnect the item changed signal to prevent errors during table reconstruction
        try:
            self.table.itemChanged.disconnect()
        except:
            pass  # Signal might not be connected yet

        # Set up table
        self.table.setRowCount(len(data))
        self.table.setColumnCount(len(data.columns))

        # Store DataFrame column names for later use in calculations
        self._dataframe_columns = data.columns.tolist()

        # Update column headers with two-row structure
        headers = data.columns.tolist()
        for i, header in enumerate(headers):
            if header == 'CustomerCode':
                headers[i] = 'Cus'
            elif header == 'ItemCode':
                headers[i] = 'Item'
            elif header == 'SalesmanCode':
                headers[i] = 'Sales\nman'
            elif header == 'DDate':
                headers[i] = 'Date'
            elif header == 'CostPrice':
                headers[i] = 'Cost\nPrice'
            elif header == 'UnitPrice':
                headers[i] = 'Unit\nPrice'
            elif header == 'NEW CostPrice':
                headers[i] = 'NEW Cost\nPrice'
            elif header == 'NEW GP':
                headers[i] = 'NEW\nGP'
            elif header == 'NEW GP%':
                headers[i] = 'NEW\nGP%'
            elif header == 'NEW Price':
                headers[i] = 'NEW\nPrice'

        self.table.setHorizontalHeaderLabels(headers)

        # Make NEW Cost Price and NEW GP% headers red and bold, NEW Price header green and bold
        for i, header in enumerate(headers):
            if header in ['NEW Cost\nPrice', 'NEW\nGP%']:
                header_item = self.table.horizontalHeaderItem(i)
                if header_item:
                    header_item.setForeground(Qt.red)
                    font = header_item.font()
                    font.setBold(True)
                    header_item.setFont(font)
            elif header == 'NEW\nPrice':
                header_item = self.table.horizontalHeaderItem(i)
                if header_item:
                    header_item.setForeground(Qt.green)
                    font = header_item.font()
                    font.setBold(True)
                    header_item.setFont(font)

        # Fill table with data
        for i in range(len(data)):
            for j, column in enumerate(data.columns):
                value = data.iat[i, j]

                # Format values appropriately
                if column == 'DDate':
                    # Format date as yyyy-mm-dd
                    if pd.notna(value):
                        if isinstance(value, str):
                            value = pd.to_datetime(value)
                        formatted_value = value.strftime('%Y-%m-%d')
                    else:
                        formatted_value = ""
                elif column in ['GP', 'NEW CostPrice', 'NEW GP', 'NEW Price']:
                    formatted_value = f"{value:.2f}"
                elif column in ['GP%', 'NEW GP%']:
                    formatted_value = f"{value:.2f}%"
                else:
                    formatted_value = str(value)

                item = QTableWidgetItem(formatted_value)

                # Right align numeric columns
                if column in ['CostPrice', 'UnitPrice', 'GP', 'GP%', 'NEW CostPrice', 'NEW GP', 'NEW GP%', 'NEW Price']:
                    item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)

                # Make only NEW CostPrice and NEW GP% editable with red text and bold, NEW Price selectable with green background
                if column == 'NEW CostPrice':
                    item.setFlags(item.flags() | Qt.ItemIsEditable)
                    item.setForeground(Qt.red)
                    font = item.font()
                    font.setBold(True)
                    item.setFont(font)
                elif column == 'NEW GP%':
                    item.setFlags(item.flags() | Qt.ItemIsEditable)
                    item.setForeground(Qt.red)
                    font = item.font()
                    font.setBold(True)
                    item.setFont(font)
                elif column == 'NEW Price':
                    item.setFlags(item.flags() & ~Qt.ItemIsEditable)  # Selectable but not editable
                    item.setForeground(Qt.green)
                    font = item.font()
                    font.setBold(True)
                    item.setFont(font)
                else:
                    item.setFlags(item.flags() & ~Qt.ItemIsEditable)

                self.table.setItem(i, j, item)

        # Set specific column width for placeholder column
        for j, column in enumerate(data.columns):
            if column == '  ':  # Placeholder column
                self.table.setColumnWidth(j, 20)  # 2 characters wide approximately

        # Connect item changed signal for color coding
        self.table.itemChanged.connect(self.on_table_item_changed)

        # Resize columns to content
        self.table.resizeColumnsToContents()

        # Set larger row height for better input visibility
        for row in range(len(data)):
            self.table.setRowHeight(row, 35)  # Increased from default ~25 to 35 pixels

    def on_table_item_changed(self, item):
        """Handle changes to editable table items"""
        row = item.row()
        col = item.column()

        # Get the actual DataFrame column names (stored during table creation)
        if not hasattr(self, '_dataframe_columns'):
            return  # No data loaded yet

        column_name = self._dataframe_columns[col]

        try:
            # Find column indices using DataFrame column names
            cost_price_col = self._dataframe_columns.index('CostPrice')
            gp_percent_col = self._dataframe_columns.index('GP%')
            new_cp_col = self._dataframe_columns.index('NEW CostPrice')
            new_gp_col = self._dataframe_columns.index('NEW GP')
            new_gp_percent_col = self._dataframe_columns.index('NEW GP%')
            new_price_col = self._dataframe_columns.index('NEW Price')

            # Check if table items exist before accessing them
            cost_price_item = self.table.item(row, cost_price_col)
            gp_percent_item = self.table.item(row, gp_percent_col)

            if cost_price_item is None or gp_percent_item is None:
                return  # Table is being rebuilt, skip processing

            original_cost = float(cost_price_item.text())
            original_gp = float(gp_percent_item.text().replace('%', ''))

            # Color coding based on changes
            if column_name == 'NEW CostPrice':
                new_value = float(item.text())
                # Always keep red text and bold for NEW CostPrice
                item.setForeground(Qt.red)
                font = item.font()
                font.setBold(True)
                item.setFont(font)

            elif column_name == 'NEW GP%':
                new_value = float(item.text().replace('%', ''))
                # Always keep red text and bold for NEW GP%
                item.setForeground(Qt.red)
                font = item.font()
                font.setBold(True)
                item.setFont(font)

            # Recalculate NEW GP and NEW Price when NEW CostPrice or NEW GP% changes
            if column_name in ['NEW CostPrice', 'NEW GP%']:
                # Check if calculation items exist
                new_cp_item = self.table.item(row, new_cp_col)
                new_gp_percent_item = self.table.item(row, new_gp_percent_col)

                if new_cp_item is None or new_gp_percent_item is None:
                    return  # Table is being rebuilt, skip processing

                new_cp = float(new_cp_item.text())
                new_gp_percent_text = new_gp_percent_item.text().replace('%', '')
                new_gp_percent = float(new_gp_percent_text)

                # Prevent division by zero when GP% is 100 or more
                if new_gp_percent >= 100:
                    new_gp_percent = 99.99  # Cap at 99.99%
                    # Update the display to show the capped value
                    item.setText(f"{new_gp_percent:.2f}%")

                # Calculate NEW Price correctly: NEW Price = NEW CostPrice / (1 - NEW GP%/100)
                new_price = new_cp / (1 - new_gp_percent / 100)
                # Calculate NEW GP: NEW GP = NEW Price - NEW CostPrice
                new_gp = new_price - new_cp

                # Update NEW GP column
                new_gp_item = QTableWidgetItem(f"{new_gp:.2f}")
                new_gp_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
                new_gp_item.setFlags(new_gp_item.flags() & ~Qt.ItemIsEditable)  # Make non-editable
                self.table.setItem(row, new_gp_col, new_gp_item)

                # Update NEW Price column
                new_price_item = QTableWidgetItem(f"{new_price:.2f}")
                new_price_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
                new_price_item.setFlags(new_price_item.flags() & ~Qt.ItemIsEditable)  # Make selectable but not editable
                new_price_item.setForeground(Qt.green)
                font = new_price_item.font()
                font.setBold(True)
                new_price_item.setFont(font)
                self.table.setItem(row, new_price_col, new_price_item)

                # Update NEW GP% to ensure it shows % symbol
                if column_name == 'NEW GP%':
                    if not item.text().endswith('%'):
                        item.setText(f"{new_gp_percent:.2f}%")

        except (ValueError, IndexError):
            # Handle invalid input
            pass

    def show_error(self, error_message):
        """Show error message"""
        QMessageBox.critical(self, "Database Error", error_message)

    def reset_fetch_button(self):
        """Reset fetch button after operation completes"""
        self.fetch_button.setEnabled(True)
        self.fetch_button.setText("Fetch Sales Data")

    def closeEvent(self, event):
        """Clean up when closing the application"""
        # Stop all timers
        self.search_timer.stop()
        self.recent_items_timer.stop()

        # Clean up main worker thread
        if self.worker and self.worker.isRunning():
            self.worker.stop()
            self.worker = None

        # Clean up recent items worker thread
        if self.recent_items_worker and self.recent_items_worker.isRunning():
            self.recent_items_worker.stop()
            self.recent_items_worker = None

        event.accept()

if __name__ == "__main__":
    app = QApplication(sys.argv)

    # Apply dark theme
    app.setStyleSheet(get_dark_stylesheet())

    window = SalesDataApp()
    window.setWindowTitle("BWH Price Tool - Sales Data Query")
    window.showMaximized()  # Make the app full screen
    sys.exit(app.exec_())