#!/usr/bin/env python3
"""
CSV Data Manager for BWH Price Tool
Replaces database queries with CSV file operations
"""

import os
import pandas as pd
from typing import List, Optional
from datetime import datetime

class CSVDataManager:
    """
    Data manager that works with CSV files instead of direct database queries
    Singleton pattern to avoid reloading data multiple times
    """
    _instance = None
    _initialized = False

    def __new__(cls, data_dir: str = "data"):
        if cls._instance is None:
            cls._instance = super(CSVDataManager, cls).__new__(cls)
        return cls._instance

    def __init__(self, data_dir: str = "data"):
        if not self._initialized:
            self.data_dir = data_dir
            self._customer_df = None
            self._sales_df = None
            self._items_df = None
            self._load_data()
            CSVDataManager._initialized = True
    
    def _load_data(self):
        """Load all CSV files into memory for fast access"""
        print("Loading CSV data files...")
        try:
            # Load customer master data
            customer_path = os.path.join(self.data_dir, "customermaster.csv")
            if os.path.exists(customer_path):
                self._customer_df = pd.read_csv(customer_path)
                print(f"✓ Loaded {len(self._customer_df)} customers from CSV")
            else:
                print(f"⚠ Warning: {customer_path} not found")
                self._customer_df = pd.DataFrame(columns=['CustomerCode', 'CustomerDesc'])

            # Load sales data
            sales_path = os.path.join(self.data_dir, "salesdata.csv")
            if os.path.exists(sales_path):
                self._sales_df = pd.read_csv(sales_path)
                # Convert DDate to datetime for proper sorting
                self._sales_df['DDate'] = pd.to_datetime(self._sales_df['DDate'])
                print(f"✓ Loaded {len(self._sales_df)} sales records from CSV")
            else:
                print(f"⚠ Warning: {sales_path} not found")
                self._sales_df = pd.DataFrame(columns=['CustomerCode', 'ItemCode', 'SalesmanCode',
                                                     'DDate', 'Description', 'CostPrice', 'Qty', 'UnitPrice'])

            # Load items data
            items_path = os.path.join(self.data_dir, "items.csv")
            if os.path.exists(items_path):
                self._items_df = pd.read_csv(items_path)
                print(f"✓ Loaded {len(self._items_df)} items from CSV")
            else:
                print(f"⚠ Warning: {items_path} not found")
                self._items_df = pd.DataFrame(columns=['ItemCode', 'Description'])

            print("✓ CSV data loading complete!")
                
        except Exception as e:
            print(f"Error loading CSV data: {e}")
            # Initialize empty DataFrames as fallback
            self._customer_df = pd.DataFrame(columns=['CustomerCode', 'CustomerDesc'])
            self._sales_df = pd.DataFrame(columns=['CustomerCode', 'ItemCode', 'SalesmanCode', 
                                                 'DDate', 'Description', 'CostPrice', 'Qty', 'UnitPrice'])
            self._items_df = pd.DataFrame(columns=['ItemCode', 'Description'])
    
    def get_customers(self) -> pd.DataFrame:
        """Get all customers (equivalent to CustomerMaster query)"""
        return self._customer_df.copy()
    
    def get_items(self) -> pd.DataFrame:
        """Get distinct item codes (equivalent to HistoryLines ItemCode query)"""
        if self._sales_df.empty:
            return pd.DataFrame(columns=['ItemCode'])
        
        # Get unique item codes from sales data
        unique_items = self._sales_df['ItemCode'].drop_duplicates().sort_values()
        return pd.DataFrame({'ItemCode': unique_items})
    
    def get_recent_items_for_customer(self, customer_code: str, limit: int = 15) -> pd.DataFrame:
        """Get recent items sold to a specific customer"""
        if self._sales_df.empty:
            return pd.DataFrame(columns=['ItemCode', 'Description', 'LastSaleDate'])

        # Trim whitespace from customer code for matching
        customer_code = customer_code.strip()

        # Filter by customer code (also trim CSV customer codes for comparison)
        customer_sales = self._sales_df[self._sales_df['CustomerCode'].astype(str).str.strip() == customer_code].copy()

        if customer_sales.empty:
            return pd.DataFrame(columns=['ItemCode', 'Description', 'LastSaleDate'])

        # Trim ItemCode, Description, and SalesmanCode to ensure proper grouping
        customer_sales['ItemCode'] = customer_sales['ItemCode'].astype(str).str.strip()
        customer_sales['Description'] = customer_sales['Description'].astype(str).str.strip()
        customer_sales['SalesmanCode'] = customer_sales['SalesmanCode'].astype(str).str.strip()

        # Sort by date descending to get most recent records first
        customer_sales = customer_sales.sort_values('DDate', ascending=False)

        # Get the most recent sale record for each ItemCode (this gives us the most recent description too)
        recent_items = (customer_sales.groupby('ItemCode')
                       .first()  # Take the first (most recent) record for each ItemCode
                       .reset_index()
                       .rename(columns={'DDate': 'LastSaleDate'}))

        # Select only the columns we need and limit results
        recent_items = recent_items[['ItemCode', 'Description', 'LastSaleDate']].head(limit)
        
        return recent_items
    
    def get_sales_data(self, customer_code: str, item_code: str, limit: int = 10) -> pd.DataFrame:
        """Get sales data for specific customer and item combination"""
        if self._sales_df.empty:
            return pd.DataFrame(columns=['CustomerCode', 'ItemCode', 'SalesmanCode',
                                       'DDate', 'Description', 'CostPrice', 'Qty', 'UnitPrice'])

        # Trim whitespace from codes for matching
        customer_code = customer_code.strip()
        item_code = item_code.strip()

        # Filter by customer and item code (trim CSV codes for comparison)
        filtered_data = self._sales_df[
            (self._sales_df['CustomerCode'].astype(str).str.strip() == customer_code) &
            (self._sales_df['ItemCode'].astype(str).str.strip() == item_code)
        ].copy()

        if not filtered_data.empty:
            # Trim Description and SalesmanCode for consistency
            filtered_data['Description'] = filtered_data['Description'].astype(str).str.strip()
            filtered_data['SalesmanCode'] = filtered_data['SalesmanCode'].astype(str).str.strip()
        
        if filtered_data.empty:
            return pd.DataFrame(columns=['CustomerCode', 'ItemCode', 'SalesmanCode', 
                                       'DDate', 'Description', 'CostPrice', 'Qty', 'UnitPrice'])
        
        # Sort by date descending and limit results
        result = (filtered_data.sort_values('DDate', ascending=False)
                 .head(limit)
                 .copy())
        
        return result
    
    def test_connection(self) -> bool:
        """Test if CSV files are available and loaded"""
        return (not self._customer_df.empty or 
                not self._sales_df.empty or 
                not self._items_df.empty)
    
    def get_data_info(self) -> dict:
        """Get information about loaded data"""
        metadata_path = os.path.join(self.data_dir, "metadata.txt")
        generated_at = "Unknown"
        
        if os.path.exists(metadata_path):
            try:
                with open(metadata_path, 'r') as f:
                    lines = f.readlines()
                    for line in lines:
                        if line.startswith("Generated:"):
                            generated_at = line.split("Generated:", 1)[1].strip()
                            break
            except:
                pass
        
        return {
            "customers_count": len(self._customer_df),
            "sales_records_count": len(self._sales_df),
            "items_count": len(self._items_df),
            "generated_at": generated_at,
            "data_directory": os.path.abspath(self.data_dir)
        }
    
    def refresh_data(self):
        """Reload CSV files from disk"""
        print("Refreshing CSV data...")
        self._load_data()

# Compatibility functions to match DatabaseBridge interface
def query_to_dataframe_csv(query_type: str, params: Optional[List] = None) -> pd.DataFrame:
    """
    Compatibility function that mimics DatabaseBridge.query_to_dataframe
    but uses CSV data instead
    """
    manager = CSVDataManager()
    
    if "CustomerMaster" in query_type:
        return manager.get_customers()
    elif "DISTINCT ItemCode" in query_type:
        return manager.get_items()
    elif params and len(params) == 1:  # Recent items query
        return manager.get_recent_items_for_customer(params[0])
    elif params and len(params) == 2:  # Sales data query
        return manager.get_sales_data(params[0], params[1])
    else:
        return pd.DataFrame()

if __name__ == "__main__":
    # Test the CSV data manager
    manager = CSVDataManager()
    
    print("CSV Data Manager Test")
    print("=" * 30)
    
    info = manager.get_data_info()
    print(f"Data directory: {info['data_directory']}")
    print(f"Generated at: {info['generated_at']}")
    print(f"Customers: {info['customers_count']}")
    print(f"Sales records: {info['sales_records_count']}")
    print(f"Items: {info['items_count']}")
    
    if manager.test_connection():
        print("✓ CSV data loaded successfully!")
    else:
        print("✗ No CSV data available!")
