@echo off
echo BWH Price Tool
echo ==============
echo.

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo Error: Python is not installed or not in PATH
    echo Please install Python and try again.
    pause
    exit /b 1
)

REM Check if CSV data exists
if not exist "data\customermaster.csv" (
    echo Warning: CSV data files not found!
    echo Please run 'generate_csv.bat' first to create the data files.
    echo.
    pause
    exit /b 1
)

echo Starting BWH Price Tool...
echo.

REM Run the main application
python price.py

if errorlevel 1 (
    echo.
    echo Error: Application failed to start!
    pause
    exit /b 1
)
