#!/usr/bin/env python3
"""
CSV Data Generator for BWH Price Tool
Generates CSV files from database queries for offline use
"""

import os
import sys
import pandas as pd
from datetime import datetime
from db_bridge import DatabaseBridge

def generate_customer_master_csv(db, output_dir="data"):
    """Generate customermaster.csv from CustomerMaster table"""
    print("Generating customermaster.csv...")
    try:
        query = "SELECT CustomerCode, CustomerDesc FROM CustomerMaster ORDER BY CustomerCode"
        df = db.query_to_dataframe(query)
        
        if not df.empty:
            os.makedirs(output_dir, exist_ok=True)
            csv_path = os.path.join(output_dir, "customermaster.csv")
            df.to_csv(csv_path, index=False)
            print(f"✓ Generated {csv_path} with {len(df)} records")
            return True
        else:
            print("✗ No customer data found")
            return False
    except Exception as e:
        print(f"✗ Error generating customermaster.csv: {e}")
        return False

def generate_sales_data_csv(db, output_dir="data"):
    """Generate salesdata.csv from HistoryLines table"""
    print("Generating salesdata.csv...")
    try:
        # Get all sales data with SearchType = '4' (sales transactions)
        query = """
        SELECT CustomerCode, ItemCode, SalesmanCode, DDate,
               Description, CostPrice, Qty, UnitPrice
        FROM HistoryLines
        WHERE SearchType = '4' AND DDate > '2024-09-01'
        ORDER BY CustomerCode, ItemCode, DDate DESC
        """
        df = db.query_to_dataframe(query)
        
        if not df.empty:
            os.makedirs(output_dir, exist_ok=True)
            csv_path = os.path.join(output_dir, "salesdata.csv")
            df.to_csv(csv_path, index=False)
            print(f"✓ Generated {csv_path} with {len(df)} records")
            return True
        else:
            print("✗ No sales data found")
            return False
    except Exception as e:
        print(f"✗ Error generating salesdata.csv: {e}")
        return False

def generate_items_csv(db, output_dir="data"):
    """Generate items.csv with unique item codes and descriptions"""
    print("Generating items.csv...")
    try:
        # Get distinct items with their most recent description
        query = """
        SELECT ItemCode, Description
        FROM Inventory
        ORDER BY ItemCode
        """
        df = db.query_to_dataframe(query)
        
        if not df.empty:
            os.makedirs(output_dir, exist_ok=True)
            csv_path = os.path.join(output_dir, "items.csv")
            df.to_csv(csv_path, index=False)
            print(f"✓ Generated {csv_path} with {len(df)} records")
            return True
        else:
            print("✗ No item data found")
            return False
    except Exception as e:
        print(f"✗ Error generating items.csv: {e}")
        return False

def generate_metadata_file(output_dir="data"):
    """Generate metadata file with generation timestamp"""
    try:
        metadata = {
            "generated_at": datetime.now().isoformat(),
            "files": ["customermaster.csv", "salesdata.csv", "items.csv"],
            "description": "BWH Price Tool CSV data files"
        }
        
        os.makedirs(output_dir, exist_ok=True)
        metadata_path = os.path.join(output_dir, "metadata.txt")
        
        with open(metadata_path, 'w') as f:
            f.write(f"BWH Price Tool CSV Data\n")
            f.write(f"Generated: {metadata['generated_at']}\n")
            f.write(f"Files: {', '.join(metadata['files'])}\n")
            f.write(f"Description: {metadata['description']}\n")
        
        print(f"✓ Generated {metadata_path}")
        return True
    except Exception as e:
        print(f"✗ Error generating metadata: {e}")
        return False

def main():
    """Main function to generate all CSV files"""
    print("BWH Price Tool - CSV Data Generator")
    print("=" * 40)
    
    # Initialize database connection
    try:
        db = DatabaseBridge()
        print("Testing database connection...")
        if not db.test_connection():
            print("✗ Database connection failed!")
            sys.exit(1)
        print("✓ Database connection successful")
    except Exception as e:
        print(f"✗ Database initialization failed: {e}")
        sys.exit(1)
    
    # Create output directory
    output_dir = "data"
    
    # Generate CSV files
    success_count = 0
    total_files = 3
    
    if generate_customer_master_csv(db, output_dir):
        success_count += 1
    
    if generate_sales_data_csv(db, output_dir):
        success_count += 1
    
    if generate_items_csv(db, output_dir):
        success_count += 1
    
    # Generate metadata
    generate_metadata_file(output_dir)
    
    # Summary
    print("\n" + "=" * 40)
    print(f"Generation complete: {success_count}/{total_files} files generated successfully")
    
    if success_count == total_files:
        print("✓ All CSV files generated successfully!")
        print(f"Files saved in: {os.path.abspath(output_dir)}")
    else:
        print("⚠ Some files failed to generate. Check errors above.")
        sys.exit(1)

if __name__ == "__main__":
    main()
